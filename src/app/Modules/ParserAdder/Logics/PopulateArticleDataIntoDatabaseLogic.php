<?php

declare(strict_types=1);

namespace App\Modules\ParserAdder\Logics;

use App\Classes\Constants\Parser;
use App\Classes\ValueObjects\ArticleObject;
use App\Classes\ValueObjects\ParsedArticleObject;
use App\Helpers\ContentHelper;
use App\Modules\ParserAdder\Services\CreatesArticle;
use App\Modules\ParserAdder\Services\CreatesArticleMedia;
use App\Modules\ParserAdder\Services\CreatesPrediction;
use App\Modules\ParserAdder\Services\GeneratesUniqueIdForArticle;
use App\Modules\ParserAdder\Services\PopulatesArticleWordCount;
use App\Modules\ParserAdder\Services\UpdatesArticle;
use App\Repositories\ArticleRepository;
use App\Services\NewswavOutboxClient;
use Carbon\Carbon;

class PopulateArticleDataIntoDatabaseLogic {
    public function __construct(
        private GeneratesUniqueIdForArticle $generatesUniqueIdForArticle,
        private CreatesArticleMedia $createsArticleMedia,
        private PopulatesArticleWordCount $populatesArticleWordCount,
        private CreatesArticle $createsArticle,
        private UpdatesArticle $updatesArticle,
        private CreatesPrediction $createsPrediction,
        private ArticleRepository $articleRepository,
        private NewswavOutboxClient $newswavOutboxClient,
        private ContentHelper $contentHelper,
    ) {
    }

    public function execute(ParsedArticleObject $articleParsed, int $publisherId, int $channelId): string {

        $existingArticle = $this->articleRepository->findByUrlAndChannelId($articleParsed->getCanonicalURL(), $channelId);
        $uniqueId = $this->generatesUniqueIdForArticle->execute($articleParsed->getCanonicalURL(), $channelId);

        $mediaWithThumbnails = $this->createsArticleMedia->execute($articleParsed->getCoverImage(), $articleParsed->getMedia(), $publisherId, $channelId);
        // Media column in article table is mostly only used in Image Forge repo. Need to find time to remove the column and repo.
        $mediaIds            = array_map(fn ($item) => $item->getMediaId(), $mediaWithThumbnails);
        $mediaString         = substr(implode(',', $mediaIds), 0, Parser::MAX_MEDIA_LENGTH);

        $this->populatesArticleWordCount->execute($uniqueId, $articleParsed->getFullContent());

        if (in_array($channelId, Parser::CHANNELS_TO_EXCLUDE_PUBLISHED_DATE, true)) {
            $articleParsed->setPublishedDate(Carbon::now()->toDateString());
        }

        $articleObject = new ArticleObject(
            $articleParsed->getArticleId(),
            $existingArticle?->uniqueID ?? $uniqueId,
            $channelId,
            $articleParsed->getTitle(),
            $articleParsed->getDescription(),
            $articleParsed->getFullContent(),
            $articleParsed->getAuthor(),
            $articleParsed->getPublishedDate(),
            $articleParsed->getModifiedDate(),
            $articleParsed->getUrl(),
            $articleParsed->getCanonicalURL(),
            $mediaString,
            $this->contentHelper->slugifyTitle($articleParsed->getTitle()) . '-' . $uniqueId,
            $articleParsed->getContentMd5(),
        );

        if ($existingArticle === null) {
            $article = $this->createsArticle->execute($articleObject);

            if (in_array($publisherId, Parser::PUBLISHERS_TO_BYPASS_PREDICTION, true) === true) {
                $articleDataWithPredictionDataObject = $this->createsPrediction->execute($article, $mediaWithThumbnails);

                $this->newswavOutboxClient->emitMessage('feed', $articleDataWithPredictionDataObject, 'contentId', 'internal.static.prediction');
            }
        } else {
            $this->updatesArticle->execute($articleObject);
        }

        return $uniqueId;

    }
}
