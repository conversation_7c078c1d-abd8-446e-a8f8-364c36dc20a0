<?php

declare(strict_types=1);

namespace App\Helpers;

use App\Classes\Constants\Parser;
use DOMDocument;
use SimpleXMLElement;

class ContentHelper {
    public function isContentRss(string $content): bool {
        libxml_use_internal_errors(true);
        $xml = simplexml_load_string($content);
        if ($xml === false) {
            return false;
        }

        $rootName = strtolower($xml->getName());

        return in_array($rootName, ['rss', 'rdf', 'feed'], true);
    }

    public function slugifyTitle(string $title): string {
        $string = preg_replace('/[^\\pL\d_]+/u', '-', $title);
        $string = trim(strtolower($string), '-');
        $string = preg_replace("/[\s-]+/", ' ', $string);
        $string = preg_replace("/[\s_]/", '-', $string);
        $string = preg_replace("/[^a-z0-9\.\-\P{Han}]+/i", '', $string);
        $string = trim(mb_substr($string, 0, 75), '-');

        return $string;
    }

    public function getCanonicalUrl(string $url): string {
        $decodedUrl = html_entity_decode($url, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        if (filter_var($decodedUrl, FILTER_VALIDATE_URL) === false) {
            return $decodedUrl;
        }

        preg_match('/^([^?]+)(\?(.+))?/', $decodedUrl, $matches);

        $base  = rtrim($matches[1] ?? $url, '/');
        $query = $matches[3] ?? '';

        if ($query === '') {
            return $base;
        }

        parse_str($query, $parsed);
        $params = array_intersect_key($parsed, array_flip(Parser::ALLOWED_ARTICLE_URL_QUERY_PARAMETERS));

        return $base . ($params ? '?' . http_build_query($params) : '');
    }

    public function cleanRawData(string $content): string {
        libxml_use_internal_errors(true);
        $dom = new DOMDocument();
        $dom->loadHTML($content, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);

        $styles = $dom->getElementsByTagName('style');
        for ($i = $styles->length - 1; $i >= 0; $i--) {
            $styles->item($i)->parentNode->removeChild($styles->item($i));
        }

        $cleaned = $dom->saveHTML() ?: $content;

        libxml_clear_errors();

        return trim($cleaned);
    }

    public function getContentFromRawRssItem(string $rawArticleData): array {
        $rssItem = new SimpleXMLElement($rawArticleData);

        $fullContent = '';

        if (isset($rssItem->{'content:encoded'})) {
            $fullContent = (string) $rssItem->{'content:encoded'};
            unset($rssItem->{'content:encoded'});
        }

        if ($fullContent === '' && isset($rssItem->content)) {
            $fullContent = (string) $rssItem->content;
            unset($rssItem->content);
        } elseif ($fullContent === '' && isset($rssItem->description)) {
            $fullContent = (string) $rssItem->description;
            unset($rssItem->description);
        }

        $dom                     = new DOMDocument();
        $dom->preserveWhiteSpace = false;
        $dom->formatOutput       = false;
        $dom->loadXML($rssItem->asXML());

        return [
            'full_content'     => $fullContent,
            'filtered_content' => $dom->saveXML($dom->documentElement),
        ];
    }

    public function getBodyFromHtmlContent(string $content): string {
        $dom = new DOMDocument();
        libxml_use_internal_errors(true); // suppress warnings for malformed HTML

        $dom->loadHTML($content, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);

        $body = $dom->getElementsByTagName('body')->item(0);

        if ( ! $body) {
            return '';
        }

        return $dom->saveHTML($body);
    }

    public function decodeHtmlEntities(string $content): string {
        return html_entity_decode($content, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    }
}
