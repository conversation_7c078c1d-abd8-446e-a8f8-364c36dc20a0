<?php

declare(strict_types=1);

namespace Tests\Unit\Modules\ParserAdder\Logics;

use App\Classes\ValueObjects\ArticleObject;
use App\Classes\ValueObjects\ParsedArticleObject;
use App\Helpers\ContentHelper;
use App\Modules\ParserAdder\Logics\PopulateArticleDataIntoDatabaseLogic;
use App\Modules\ParserAdder\Services\CreatesArticle;
use App\Modules\ParserAdder\Services\CreatesArticleMedia;
use App\Modules\ParserAdder\Services\CreatesPrediction;
use App\Modules\ParserAdder\Services\GeneratesUniqueIdForArticle;
use App\Modules\ParserAdder\Services\PopulatesArticleWordCount;
use App\Modules\ParserAdder\Services\UpdatesArticle;
use App\Repositories\ArticleRepository;
use App\Services\NewswavOutboxClient;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Mockery;
use Tests\TestCase;

class PopulateArticleDataIntoDatabaseLogicRaceConditionTest extends TestCase {
    public function testItHandlesRaceConditionWhenArticleIsDeletedDuringUpdate(): void {
        $publisherId = 1;
        $channelId = 2;
        $uniqueId = 'A2509_test123';
        $canonicalUrl = 'https://example.com/article';

        // Create mocks
        $parsedArticle = Mockery::mock(ParsedArticleObject::class);
        $existingArticle = $this->createArticle(['uniqueID' => $uniqueId]);
        
        $articleRepositoryMock = Mockery::mock(ArticleRepository::class);
        $generatesUniqueIdMock = Mockery::mock(GeneratesUniqueIdForArticle::class);
        $createsArticleMediaMock = Mockery::mock(CreatesArticleMedia::class);
        $populatesWordCountMock = Mockery::mock(PopulatesArticleWordCount::class);
        $createsArticleMock = Mockery::mock(CreatesArticle::class);
        $updatesArticleMock = Mockery::mock(UpdatesArticle::class);
        $createsPredictionMock = Mockery::mock(CreatesPrediction::class);
        $newswavOutboxClientMock = Mockery::mock(NewswavOutboxClient::class);
        $contentHelperMock = Mockery::mock(ContentHelper::class);

        // Setup parsed article mock
        $parsedArticle->shouldReceive('getCanonicalURL')->andReturn($canonicalUrl);
        $parsedArticle->shouldReceive('getCoverImage')->andReturn(['url' => 'cover.jpg', 'caption' => null]);
        $parsedArticle->shouldReceive('getMedia')->andReturn([]);
        $parsedArticle->shouldReceive('getFullContent')->andReturn('Article content');
        $parsedArticle->shouldReceive('setPublishedDate')->once();
        $parsedArticle->shouldReceive('getArticleId')->andReturn('123');
        $parsedArticle->shouldReceive('getTitle')->andReturn('Test Article');
        $parsedArticle->shouldReceive('getDescription')->andReturn('Test Description');
        $parsedArticle->shouldReceive('getAuthor')->andReturn('Test Author');
        $parsedArticle->shouldReceive('getPublishedDate')->andReturn('2024-01-01');
        $parsedArticle->shouldReceive('getModifiedDate')->andReturn('2024-01-01');
        $parsedArticle->shouldReceive('getUrl')->andReturn($canonicalUrl);
        $parsedArticle->shouldReceive('getContentMd5')->andReturn('md5hash');

        // Setup repository mock - article exists initially but gets deleted during update
        $articleRepositoryMock->shouldReceive('findByUrlAndChannelId')
            ->with($canonicalUrl, $channelId)
            ->once()
            ->andReturn($existingArticle);

        // Setup other service mocks
        $generatesUniqueIdMock->shouldReceive('execute')
            ->with($canonicalUrl, $channelId)
            ->once()
            ->andReturn($uniqueId);

        $createsArticleMediaMock->shouldReceive('execute')->once()->andReturn([]);
        $populatesWordCountMock->shouldReceive('execute')->once();
        $contentHelperMock->shouldReceive('slugifyTitle')->once()->andReturn('test-article');

        // Mock the update to throw ModelNotFoundException (simulating race condition)
        $updatesArticleMock->shouldReceive('execute')
            ->once()
            ->andThrow(new ModelNotFoundException('Unable to find record where uniqueID = ' . $uniqueId));

        // Mock the fallback create operation
        $newArticle = $this->createArticle(['uniqueID' => $uniqueId]);
        $createsArticleMock->shouldReceive('execute')
            ->once()
            ->with(Mockery::type(ArticleObject::class))
            ->andReturn($newArticle);

        // Create the logic instance
        $logic = new PopulateArticleDataIntoDatabaseLogic(
            $generatesUniqueIdMock,
            $createsArticleMediaMock,
            $populatesWordCountMock,
            $createsArticleMock,
            $updatesArticleMock,
            $createsPredictionMock,
            $articleRepositoryMock,
            $newswavOutboxClientMock,
            $contentHelperMock,
        );

        // Execute and verify it doesn't throw an exception
        $result = $logic->execute($parsedArticle, $publisherId, $channelId);

        $this->assertEquals($uniqueId, $result);
    }
}
